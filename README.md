# ElectronicController_Board

本仓库包含米醋电子工作室开发的电子控制器板相关代码。

## 激光笔控制功能

本项目新增了激光笔控制功能，通过GPIO口控制MOS模块来开关激光笔。

### 硬件连接
- **控制引脚**: PB12 (GPIO输出)
- **MOS模块**: 连接到PB12，用于控制激光笔的电源开关
- **激光笔**: 通过MOS模块控制，支持开关和闪烁功能

### 控制命令
通过串口发送以下命令格式来控制激光笔：

#### 二进制命令格式
- `$L1` - 开启激光笔
- `$L0` - 关闭激光笔
- `$LT` - 切换激光笔状态
- `$LB[周期高字节][周期低字节][次数高字节][次数低字节]` - 闪烁控制

#### 字符串命令格式（内部使用）
- `L:ON` - 开启激光笔
- `L:OFF` - 关闭激光笔
- `L:TOGGLE` - 切换状态
- `L:BLINK,500,10` - 闪烁500ms周期，10次
- `L:BLINK,1000,0` - 无限闪烁，1000ms周期

### 使用示例
```c
// 开启激光笔
app_laser_on();

// 关闭激光笔
app_laser_off();

// 切换状态
app_laser_toggle();

// 闪烁10次，每次500ms
app_laser_blink(500, 10);

// 无限闪烁，每次1000ms
app_laser_blink(1000, 0);

// 获取当前状态
LaserState_t state = app_laser_get_state();
```

### OLED显示
激光笔状态会在OLED屏幕上实时显示：
- `Laser: OFF` - 激光笔关闭
- `Laser: ON` - 激光笔开启
- `Laser: BLINK` - 激光笔闪烁中

## Web 上位机

为了方便调试和控制 PID 控制器 (`App/app_pid.c`)，本项目提供了一个基于 Web 的上位机界面。

### 功能

*   **串口连接**: 自动检测可用串口，支持设置波特率并连接/断开设备。
*   **状态显示**: 实时显示 X/Y 轴的目标位置、实际位置和电机输出速度。
*   **位置曲线**: 在同一个图表中实时绘制 X 轴目标/实际值 和 Y 轴目标/实际值 随时间变化的曲线。
*   **PID 曲线**: （需设备端开启调试）实时绘制 X/Y 轴 PID 控制器的 P, I, D 分量输出曲线。
*   **图表交互**: 所有曲线图表的 Y 轴支持通过鼠标滚轮或触摸板手势进行缩放，通过鼠标拖拽进行平移。
*   **PID 控制**: 发送启动和停止 PID 控制的命令。
*   **目标设置**: 通过输入框设置 X/Y 轴的目标坐标。
*   **参数调节**: 通过滑动条实时调整 X/Y 轴的 PID 参数 (Kp, Ki, Kd)。
*   **日志输出**: 显示从设备接收到的原始数据和上位机操作日志。
*   **数据保留**: 图表数据（最近 100 个点）会自动保存在浏览器本地存储中，刷新页面或下次访问时会尝试恢复。

### 使用方法

1.  **安装依赖**: 确保您的计算机已安装 Python 3 和 pip。
2.  **进入目录**: 在终端或命令行中，导航到 `Web` 目录。
3.  **运行启动脚本**:
    *   **Windows**: 双击运行 `start.bat`。
    *   **Linux/macOS**: 在终端中运行 `chmod +x start.sh` (首次运行时需要赋予执行权限)，然后运行 `./start.sh`。
4.  **启动服务**: 脚本会自动安装所需的 Python 库并启动 Web 服务器。
5.  **访问上位机**: 脚本启动后，会提示服务器地址 (通常是 `http://127.0.0.1:5000` 或 `http://0.0.0.0:5000`)。在您的网页浏览器中打开此地址。
6.  **连接设备**: 在网页界面中选择您的设备连接的串口号，确认波特率（默认为 115200，与 `app_pid.c` 中使用的 `my_printf` 相关串口配置应一致），然后点击"连接"按钮。
7.  **操作**: 连接成功后，即可使用界面上的各种功能进行监控和控制。

### 注意事项

*   图表数据保留功能依赖浏览器本地存储（LocalStorage）。如果浏览器禁用或清除本地存储，数据将丢失。
*   要查看 PID 实时曲线，需要确保设备端代码 (`App/app_pid.c` 或 `App/app_pid.h`) 中定义了 `PID_DEBUG_ENABLE` 为 1，并重新编译烧录固件。
*   确保设备通过 `huart1` (根据 `App/app_pid.h` 中的定义) 连接到计算机，并在上位机界面中选择了正确的 COM 端口。
*   确保设备固件中的波特率与上位机设置的波特率一致。
*   上位机与设备之间的通信协议细节基于 `App/app_pid.c` 中的 `app_pid_report` 和 `app_pid_parse_cmd` 函数实现。如果修改了设备端的协议，需要同步更新 `Web/server.py` 中的解析和命令格式代码。

---
Copyright (c) 2024 米醋电子工作室. 保留所有权利。 