/**
 * @file app_laser.h
 * @brief 激光笔控制模块头文件
 * @copyright 米醋电子工作室
 */

#ifndef __APP_LASER_H_
#define __APP_LASER_H_

#include "mydefine.h"

/* 激光笔控制引脚定义 */
#define LASER_GPIO_PORT     GPIOB           // 激光笔控制GPIO端口
#define LASER_GPIO_PIN      GPIO_PIN_12     // 激光笔控制GPIO引脚
#define LASER_GPIO_CLK()    __HAL_RCC_GPIOB_CLK_ENABLE()  // GPIO时钟使能

/* 激光笔控制宏定义 */
#define LASER_ON()          HAL_GPIO_WritePin(LASER_GPIO_PORT, LASER_GPIO_PIN, GPIO_PIN_SET)    // 激光笔开启
#define LASER_OFF()         HAL_GPIO_WritePin(LASER_GPIO_PORT, LASER_GPIO_PIN, GPIO_PIN_RESET)  // 激光笔关闭
#define LASER_TOGGLE()      HAL_GPIO_TogglePin(LASER_GPIO_PORT, LASER_GPIO_PIN)                 // 激光笔状态切换

/* 激光笔状态枚举 */
typedef enum {
    LASER_STATE_OFF = 0,    // 关闭状态
    LASER_STATE_ON = 1,     // 开启状态
    LASER_STATE_BLINK = 2   // 闪烁状态
} LaserState_t;

/* 激光笔配置结构体 */
typedef struct {
    LaserState_t state;     // 当前状态
    uint16_t blink_period;  // 闪烁周期(ms)
    uint16_t blink_count;   // 闪烁次数(0=无限)
    uint8_t brightness;     // 亮度(0-100%, 预留PWM功能)
} LaserConfig_t;

/* 函数声明 */
void app_laser_init(void);                                      // 激光笔初始化
void app_laser_on(void);                                        // 开启激光笔
void app_laser_off(void);                                       // 关闭激光笔
void app_laser_toggle(void);                                    // 切换激光笔状态
void app_laser_blink(uint16_t period_ms, uint16_t count);       // 激光笔闪烁
void app_laser_set_brightness(uint8_t brightness);              // 设置亮度(预留)
LaserState_t app_laser_get_state(void);                         // 获取激光笔状态
void app_laser_task(MultiTimer *timer, void *userData);         // 激光笔任务(处理闪烁)
void app_laser_parse_cmd(char *cmd);                            // 解析激光笔控制命令

#endif /* __APP_LASER_H_ */
