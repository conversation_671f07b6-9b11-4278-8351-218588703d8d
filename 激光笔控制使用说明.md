# 激光笔控制功能使用说明

## 概述
本项目为STM32F4电子控制板添加了激光笔控制功能，通过GPIO口控制MOS模块来实现激光笔的开关和闪烁控制。

## 硬件配置

### 1. CubeMX配置
- **GPIO引脚**: PB12
- **模式**: GPIO_Output
- **输出类型**: Push Pull
- **上拉/下拉**: No pull-up and no pull-down
- **最大输出速度**: Low

### 2. 硬件连接
```
STM32F407VET6 (PB12) --> MOS模块控制端 --> 激光笔电源控制
```

## 软件实现

### 1. 文件结构
```
App/
├── app_laser.h          # 激光笔控制头文件
├── app_laser.c          # 激光笔控制实现
├── laser_test.h         # 测试程序头文件
├── laser_test.c         # 测试程序实现
└── mydefine.h           # 全局定义(已更新)
```

### 2. 主要功能
- **基本控制**: 开启、关闭、状态切换
- **闪烁控制**: 支持设定周期和次数的闪烁
- **状态监控**: 实时获取激光笔状态
- **命令解析**: 支持串口命令控制
- **OLED显示**: 在屏幕上显示激光笔状态

## 使用方法

### 1. 初始化
```c
// 在系统初始化中调用
app_laser_init();
```

### 2. 基本控制
```c
// 开启激光笔
app_laser_on();

// 关闭激光笔
app_laser_off();

// 切换状态
app_laser_toggle();
```

### 3. 闪烁控制
```c
// 闪烁10次，每次500ms周期
app_laser_blink(500, 10);

// 无限闪烁，1000ms周期
app_laser_blink(1000, 0);
```

### 4. 状态查询
```c
LaserState_t state = app_laser_get_state();
switch(state) {
    case LASER_STATE_OFF:
        // 激光笔关闭
        break;
    case LASER_STATE_ON:
        // 激光笔开启
        break;
    case LASER_STATE_BLINK:
        // 激光笔闪烁中
        break;
}
```

### 5. 串口命令控制
发送以下格式的命令到串口：

#### 二进制命令
- `$L1` - 开启激光笔
- `$L0` - 关闭激光笔
- `$LT` - 切换状态
- `$LB[周期高][周期低][次数高][次数低]` - 闪烁控制

#### 示例
```c
// 通过串口发送开启命令
uint8_t cmd[] = {'$', 'L', '1'};
HAL_UART_Transmit(&huart1, cmd, 3, 100);

// 通过串口发送闪烁命令(500ms周期，5次)
uint8_t blink_cmd[] = {'$', 'L', 'B', 0x01, 0xF4, 0x00, 0x05};
HAL_UART_Transmit(&huart1, blink_cmd, 7, 100);
```

## 测试程序

### 运行测试
```c
// 在main函数中调用完整测试
laser_full_test();

// 或者单独测试各个功能
laser_basic_test();     // 基本功能测试
laser_blink_test();     // 闪烁功能测试
laser_cmd_test();       // 命令解析测试
laser_state_test();     // 状态检测测试
```

## 注意事项

1. **安全性**: 激光笔使用时注意安全，避免直射眼睛
2. **电流限制**: 确保MOS模块能承受激光笔的工作电流
3. **散热**: 长时间使用时注意散热
4. **电源**: 确保电源供应充足
5. **GPIO配置**: 必须在CubeMX中正确配置PB12为GPIO输出

## 故障排除

### 1. 激光笔不工作
- 检查硬件连接
- 检查MOS模块是否正常
- 检查电源供应
- 使用万用表测量PB12输出电压

### 2. 闪烁不正常
- 检查定时器是否正常工作
- 检查MultiTimer模块是否正确初始化
- 调整闪烁周期参数

### 3. 串口命令无响应
- 检查串口配置和波特率
- 检查命令格式是否正确
- 检查串口接收中断是否正常

## 扩展功能

### 1. PWM调光(预留)
可以将GPIO输出改为PWM输出，实现激光笔亮度调节：
```c
// 预留的亮度设置函数
app_laser_set_brightness(80); // 设置80%亮度
```

### 2. 更多闪烁模式
可以扩展更复杂的闪烁模式，如SOS信号等。

### 3. 安全保护
可以添加超时保护，防止激光笔长时间开启。
