/**
 * @file app_laser.c
 * @brief 激光笔控制模块实现
 * @copyright 米醋电子工作室
 */

#include "app_laser.h"

/* 全局变量 */
static LaserConfig_t laser_config = {
    .state = LASER_STATE_OFF,
    .blink_period = 500,
    .blink_count = 0,
    .brightness = 100
};

static MultiTimer mt_laser_blink;           // 激光笔闪烁定时器
static uint16_t blink_counter = 0;          // 闪烁计数器
static uint8_t blink_state = 0;             // 闪烁状态(0=关闭, 1=开启)

/**
 * @brief 激光笔GPIO初始化
 */
static void laser_gpio_init(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    
    // 使能GPIO时钟
    LASER_GPIO_CLK();
    
    // 配置GPIO
    GPIO_InitStruct.Pin = LASER_GPIO_PIN;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;     // 推挽输出
    GPIO_InitStruct.Pull = GPIO_NOPULL;             // 无上下拉
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;    // 低速
    HAL_GPIO_Init(LASER_GPIO_PORT, &GPIO_InitStruct);
    
    // 初始状态为关闭
    LASER_OFF();
}

/**
 * @brief 激光笔初始化
 */
void app_laser_init(void)
{
    // GPIO初始化
    laser_gpio_init();
    
    // 重置配置为默认状态
    laser_config.state = LASER_STATE_OFF;
    laser_config.blink_period = 500;
    laser_config.blink_count = 0;
    laser_config.brightness = 100;
    
    // 确保激光笔关闭
    LASER_OFF();
}

/**
 * @brief 开启激光笔
 */
void app_laser_on(void)
{
    // 停止闪烁定时器
    multiTimerStop(&mt_laser_blink);
    
    // 设置状态并开启激光笔
    laser_config.state = LASER_STATE_ON;
    LASER_ON();
}

/**
 * @brief 关闭激光笔
 */
void app_laser_off(void)
{
    // 停止闪烁定时器
    multiTimerStop(&mt_laser_blink);
    
    // 设置状态并关闭激光笔
    laser_config.state = LASER_STATE_OFF;
    LASER_OFF();
}

/**
 * @brief 切换激光笔状态
 */
void app_laser_toggle(void)
{
    if (laser_config.state == LASER_STATE_OFF) {
        app_laser_on();
    } else {
        app_laser_off();
    }
}

/**
 * @brief 激光笔闪烁任务
 */
void app_laser_task(MultiTimer *timer, void *userData)
{
    if (laser_config.state == LASER_STATE_BLINK) {
        // 切换闪烁状态
        if (blink_state) {
            LASER_OFF();
            blink_state = 0;
        } else {
            LASER_ON();
            blink_state = 1;
            blink_counter++;
        }
        
        // 检查是否达到闪烁次数
        if (laser_config.blink_count > 0 && blink_counter >= laser_config.blink_count) {
            // 闪烁完成，关闭激光笔
            app_laser_off();
            blink_counter = 0;
        } else {
            // 继续闪烁
            multiTimerStart(&mt_laser_blink, laser_config.blink_period / 2, app_laser_task, NULL);
        }
    }
}

/**
 * @brief 激光笔闪烁
 * @param period_ms 闪烁周期(ms)
 * @param count 闪烁次数(0=无限闪烁)
 */
void app_laser_blink(uint16_t period_ms, uint16_t count)
{
    // 停止之前的闪烁
    multiTimerStop(&mt_laser_blink);
    
    // 设置闪烁参数
    laser_config.state = LASER_STATE_BLINK;
    laser_config.blink_period = period_ms;
    laser_config.blink_count = count;
    
    // 重置计数器
    blink_counter = 0;
    blink_state = 0;
    
    // 启动闪烁定时器
    multiTimerStart(&mt_laser_blink, period_ms / 2, app_laser_task, NULL);
}

/**
 * @brief 设置激光笔亮度(预留PWM功能)
 * @param brightness 亮度(0-100%)
 */
void app_laser_set_brightness(uint8_t brightness)
{
    if (brightness > 100) brightness = 100;
    laser_config.brightness = brightness;
    // TODO: 实现PWM调光功能
}

/**
 * @brief 获取激光笔状态
 * @return 激光笔状态
 */
LaserState_t app_laser_get_state(void)
{
    return laser_config.state;
}

/**
 * @brief 解析激光笔控制命令
 * @param cmd 命令字符串
 * 命令格式:
 * - "L:ON" - 开启激光笔
 * - "L:OFF" - 关闭激光笔
 * - "L:TOGGLE" - 切换状态
 * - "L:BLINK,500,10" - 闪烁(周期500ms,10次)
 * - "L:BLINK,1000,0" - 无限闪烁(周期1000ms)
 */
void app_laser_parse_cmd(char *cmd)
{
    int period, count;
    
    if (strncmp(cmd, "L:ON", 4) == 0) {
        app_laser_on();
    }
    else if (strncmp(cmd, "L:OFF", 5) == 0) {
        app_laser_off();
    }
    else if (strncmp(cmd, "L:TOGGLE", 8) == 0) {
        app_laser_toggle();
    }
    else if (sscanf(cmd, "L:BLINK,%d,%d", &period, &count) == 2) {
        if (period > 0 && period <= 10000) {  // 限制周期范围
            app_laser_blink((uint16_t)period, (uint16_t)count);
        }
    }
}
