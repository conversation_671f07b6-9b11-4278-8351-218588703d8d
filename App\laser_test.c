/**
 * @file laser_test.c
 * @brief 激光笔功能测试程序
 * @copyright 米醋电子工作室
 */

#include "app_laser.h"
#include "delay.h"

/**
 * @brief 激光笔基本功能测试
 */
void laser_basic_test(void)
{
    // 测试开关功能
    app_laser_on();
    delay_ms(1000);
    
    app_laser_off();
    delay_ms(1000);
    
    // 测试切换功能
    app_laser_toggle();
    delay_ms(1000);
    
    app_laser_toggle();
    delay_ms(1000);
}

/**
 * @brief 激光笔闪烁功能测试
 */
void laser_blink_test(void)
{
    // 测试闪烁5次，每次300ms
    app_laser_blink(300, 5);
    delay_ms(3000); // 等待闪烁完成
    
    // 测试快速闪烁10次，每次100ms
    app_laser_blink(100, 10);
    delay_ms(2000); // 等待闪烁完成
    
    // 测试慢速闪烁3次，每次1000ms
    app_laser_blink(1000, 3);
    delay_ms(6000); // 等待闪烁完成
}

/**
 * @brief 激光笔命令解析测试
 */
void laser_cmd_test(void)
{
    // 测试开启命令
    app_laser_parse_cmd("L:ON");
    delay_ms(1000);
    
    // 测试关闭命令
    app_laser_parse_cmd("L:OFF");
    delay_ms(1000);
    
    // 测试切换命令
    app_laser_parse_cmd("L:TOGGLE");
    delay_ms(1000);
    
    app_laser_parse_cmd("L:TOGGLE");
    delay_ms(1000);
    
    // 测试闪烁命令
    app_laser_parse_cmd("L:BLINK,200,8");
    delay_ms(3000);
}

/**
 * @brief 激光笔状态检测测试
 */
void laser_state_test(void)
{
    LaserState_t state;
    
    // 测试关闭状态
    app_laser_off();
    state = app_laser_get_state();
    // 这里可以添加断言或日志输出来验证状态
    
    // 测试开启状态
    app_laser_on();
    state = app_laser_get_state();
    // 验证状态为LASER_STATE_ON
    
    // 测试闪烁状态
    app_laser_blink(500, 0); // 无限闪烁
    delay_ms(100); // 等待闪烁开始
    state = app_laser_get_state();
    // 验证状态为LASER_STATE_BLINK
    
    app_laser_off(); // 停止闪烁
}

/**
 * @brief 激光笔完整功能测试
 */
void laser_full_test(void)
{
    // 初始化激光笔
    app_laser_init();
    
    // 基本功能测试
    laser_basic_test();
    
    // 闪烁功能测试
    laser_blink_test();
    
    // 命令解析测试
    laser_cmd_test();
    
    // 状态检测测试
    laser_state_test();
    
    // 测试完成，确保激光笔关闭
    app_laser_off();
}
